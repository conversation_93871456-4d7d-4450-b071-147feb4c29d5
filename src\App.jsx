import { BrowserRouter, Routes, Route } from 'react-router-dom'
import './App.css'
import Home from './components/Home'
import Header from './components/Header'
import Navbar from './components/Navbar'
import OurHistory from './components/OurHistory'
import Gallery from './components/Gallery'
import Donate from './components/Donate'
import Service from './components/Service'
import Contact from './components/Contact'
import Footer from './components/Footer'
import Testimonials from './components/Testimonials'
import { useEffect, useState } from 'react'
import { ChevronsUp } from 'lucide-react'
import whatsapp from './assets/icons8-whatsapp-480.png';


const ChatIcon = ({ onClick }) => (
  <div className="fixed z-50 p-0 rounded-full cursor-pointer right-1 bottom-20 hover:scale-105">
    <div>
      <div>
        <img src={whatsapp} alt="" className="w-10" />
      </div>
    </div>
  </div>
);

const ScrollToTopIcon = ({ onClick }) => (
  <div className="fixed z-50 p-0 rounded-full cursor-pointer right-2 bottom-10 hover:scale-105" onClick={onClick}>
    <div>
      <div className="bg-[#fd7e14] p-1 rounded-full">
        {/* <MoveUp className=" text-gradient text-white"/> */}
        <ChevronsUp className=" text-gradient text-white" />
      </div>
    </div>
  </div>
)

function App() {
  const [showIcons, setShowIcons] = useState(false)
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowIcons(true)
      } else {
        setShowIcons(false)
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    })
  }

  const chatBot = () => {
    setShowChatApp(!showChatApp)
  }

  const [showChatApp, setShowChatApp] = useState(false)

  return (
    <BrowserRouter>
      {
        showIcons && (
          <>
            <ChatIcon onClick={chatBot} />
            <ScrollToTopIcon onClick={scrollToTop} />
          </>
        )
      }
      <Header />
      <Navbar />
      <Routes>
        <Route path='/' element={<Home />} />
        <Route path='/our_history' element={<OurHistory />} />
        <Route path='/gallery' element={<Gallery />} />
        <Route path='/donate' element={<Donate />} />
        {/* <Route path='/service' element={<Service />} /> */}
        <Route path='/contact' element={<Contact />} />
        <Route path='/testimonials' element={<Testimonials />} />
      </Routes>
      <Footer />
    </BrowserRouter>
  )
}

export default App
