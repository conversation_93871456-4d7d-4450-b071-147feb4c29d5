import { Mail, MapPinHouse, PhoneCall } from 'lucide-react'
import React from 'react'



const Header = () => {
    return (
        <div className='bg-[#0e1d81] lg:h-[50px] px-[5vw] flex lg:justify-between lg:items-center flex-col lg:flex-row h-auto'>
            <div className='lg:w-1/2 w-full p-2'>
                <marquee className='text-white'>A Brighter Future for Children, A Respectful Tomorrow for Elders.</marquee>
            </div>
            <div className='lg:w-1/2 flex justify-center items-center gap-4 w-full'>
                <div className='flex justify-center items-center lg:gap-3 gap-1'>
                    <Mail className='text-white' />
                    <h3 className='text-white text-sm'><EMAIL></h3>
                </div>
                <div className='flex justify-center items-center lg:gap-3 gap-1'>
                    <PhoneCall className='text-white' />
                    <h3 className='text-white text-sm'>9440041850/7731041850</h3>
                </div>
            </div>
        </div>
    )
}

export default Header