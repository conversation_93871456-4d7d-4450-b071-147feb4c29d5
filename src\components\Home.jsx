import { useState, useEffect } from "react"
import AOS from "aos"
import "aos/dist/aos.css"
import {
  Heart,
  Users,
  BookOpen,
  HomeIcon,
  Award,
  ArrowRight,
  Menu,
  X,
  Phone,
  Mail,
  MapPin,
  Star,
  ChevronLeft,
  ChevronRight,
  Play,
  Sparkles,
  Target,
  Globe,
  Shield,
} from "lucide-react"

const ModernCharityWebsite = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    // Initialize AOS with enhanced settings
    AOS.init({
      duration: 1200,
      once: true,
      easing: "ease-out-cubic",
      offset: 100,
    })
  }, [])

  // Enhanced hero carousel with better content
  const heroSlides = [
    {
      image:
        "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "Empowering Through Education",
      subtitle: "Every child deserves quality education to unlock their infinite potential",
      overlay: "from-indigo-900/90 via-purple-900/80 to-pink-900/70",
      accent: "from-yellow-400 to-orange-500",
    },
    {
      image:
        "https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "Healthcare for All",
      subtitle: "Providing essential medical care to underserved communities worldwide",
      overlay: "from-emerald-900/90 via-teal-900/80 to-cyan-900/70",
      accent: "from-emerald-400 to-teal-500",
    },
    {
      image:
        "https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "Safe Shelter & Care",
      subtitle: "Creating loving homes where children can thrive and dream big",
      overlay: "from-rose-900/90 via-pink-900/80 to-purple-900/70",
      accent: "from-rose-400 to-pink-500",
    },
    {
      image:
        "https://images.unsplash.com/photo-**********-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "Community Development",
      subtitle: "Building stronger, more resilient communities through collaboration",
      overlay: "from-amber-900/90 via-orange-900/80 to-red-900/70",
      accent: "from-amber-400 to-orange-500",
    },
  ]

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Auto-advance carousel with pause on hover
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)
  }

  const programs = [
    {
      icon: <BookOpen className="w-14 h-14 text-indigo-600" />,
      title: "Education Support",
      description:
        "Comprehensive educational programs including digital literacy, STEM education, and scholarship opportunities to break the cycle of poverty through knowledge.",
      beneficiaries: "500+ Children",
      color: "from-indigo-500 to-purple-600",
      bgColor: "from-indigo-50 to-purple-50",
      image:
        "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    },
    {
      icon: <Heart className="w-14 h-14 text-rose-600" />,
      title: "Healthcare Initiative",
      description:
        "Holistic healthcare services including preventive care, mental health support, nutrition programs, and emergency medical assistance for vulnerable communities.",
      beneficiaries: "1000+ Lives",
      color: "from-rose-500 to-pink-600",
      bgColor: "from-rose-50 to-pink-50",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    },
    {
      icon: <HomeIcon className="w-14 h-14 text-emerald-600" />,
      title: "Shelter & Care",
      description:
        "Safe, nurturing environments with 24/7 care, counseling services, and family reunification programs for children rescued from difficult circumstances.",
      beneficiaries: "250+ Children",
      color: "from-emerald-500 to-teal-600",
      bgColor: "from-emerald-50 to-teal-50",
      image:
        "https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    },
    {
      icon: <Users className="w-14 h-14 text-amber-600" />,
      title: "Community Development",
      description:
        "Sustainable development programs including microfinance, skill development, women empowerment, and environmental conservation initiatives.",
      beneficiaries: "800+ Families",
      color: "from-amber-500 to-orange-600",
      bgColor: "from-amber-50 to-orange-50",
      image: "https://images.unsplash.com/photo-**********-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    },
  ]

  const stats = [
    {
      number: "2,500+",
      label: "Lives Transformed",
      icon: <Heart className="w-10 h-10" />,
      color: "from-rose-500 to-pink-600",
      description: "Children and families empowered",
    },
    {
      number: "15+",
      label: "Years of Service",
      icon: <Award className="w-10 h-10" />,
      color: "from-amber-500 to-orange-600",
      description: "Dedicated to making change",
    },
    {
      number: "50+",
      label: "Community Partners",
      icon: <Users className="w-10 h-10" />,
      color: "from-indigo-500 to-purple-600",
      description: "Organizations working together",
    },
    {
      number: "₹2.5Cr+",
      label: "Funds Raised",
      icon: <Star className="w-10 h-10" />,
      color: "from-emerald-500 to-teal-600",
      description: "Invested in communities",
    },
  ]

  const impactStories = [
    {
      image:
        "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      title: "Classroom Excellence",
      description: "250+ children now have access to world-class education with modern facilities",
      category: "Education",
      color: "from-indigo-600 to-purple-600",
    },
    {
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      title: "Healthcare Access",
      description: "Comprehensive health services ensuring every child gets the care they deserve",
      category: "Healthcare",
      color: "from-rose-600 to-pink-600",
    },
    {
      image:
        "https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      title: "Community Growth",
      description: "Empowering communities to become self-sufficient and prosperous",
      category: "Development",
      color: "from-emerald-600 to-teal-600",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50">
      {/* Enhanced Navigation */}
   

      {/* Enhanced Hero Section */}
      <section id="home" className="relative min-h-screen overflow-hidden flex items-center justify-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-rose-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* Carousel Background - Only Image, No Overlay */}
        <div className="absolute inset-0">
          {heroSlides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-all duration-1000 ${
                index === currentSlide ? "opacity-100 scale-100" : "opacity-0 scale-105"
              }`}
            >
              <img src={slide.image || "/placeholder.svg"} alt={slide.title} className="w-full h-full object-cover" />
            </div>
          ))}
        </div>

        {/* Navigation Buttons */}
        <button
          onClick={prevSlide}
          className="absolute left-8 top-1/2 transform -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md shadow-2xl rounded-full p-5 hover:bg-white/40 transition-all duration-300 group border border-white/30 hover:scale-110"
        >
          <ChevronLeft className="w-8 h-8 text-white group-hover:scale-110 transition-transform" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-8 top-1/2 transform -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md shadow-2xl rounded-full p-5 hover:bg-white/40 transition-all duration-300 group border border-white/30 hover:scale-110"
        >
          <ChevronRight className="w-8 h-8 text-white group-hover:scale-110 transition-transform" />
        </button>

        {/* Carousel Indicators */}
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
          {heroSlides.map((slide, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`relative transition-all duration-500 ${
                index === currentSlide
                  ? "w-12 h-4 rounded-full bg-gradient-to-r from-white to-indigo-200 shadow-lg"
                  : "w-4 h-4 rounded-full bg-white/60 hover:bg-white/90"
              }`}
            >
              {index === currentSlide && (
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-400 to-purple-400 animate-pulse"></div>
              )}
            </button>
          ))}
        </div>

        {/* Hero Content - No Morphism, No Background Color */}
        {/* Removed all overlay content from the carousel as requested */}
      </section>

      {/* Enhanced Stats Section */}
      <section className="py-24 bg-white/90 backdrop-blur-xl relative overflow-hidden" data-aos="fade-up">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-50/50 to-purple-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-4">
              Our{" "}
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Impact
              </span>{" "}
              in Numbers
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Real results that showcase the difference we're making together
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 p-8 border border-gray-100 hover:scale-105 overflow-hidden"
                data-aos="zoom-in"
                data-aos-delay={index * 100}
              >
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                ></div>

                <div className="relative text-center">
                  <div
                    className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${stat.color} mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <div className="text-white">{stat.icon}</div>
                  </div>

                  <div className="text-4xl md:text-5xl font-black text-gray-900 mb-3 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                  </div>

                  <div className="text-lg font-bold text-gray-800 mb-2">{stat.label}</div>

                  <div className="text-sm text-gray-600">{stat.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced About Section */}
      <section
        id="about"
        className="py-24 bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden"
        data-aos="fade-up"
      >
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-r from-indigo-200/30 to-purple-200/30 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-rose-200/30 to-pink-200/30 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-indigo-100 rounded-full px-6 py-3 mb-6">
              <Target className="w-5 h-5 text-indigo-600 mr-2" />
              <span className="text-indigo-800 font-semibold">OUR MISSION</span>
            </div>

            <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
              Building a World of
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                {" "}
                Hope
              </span>
            </h2>

            <p className="text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              We envision a world where every child has access to education, healthcare, and the opportunity to reach
              their full potential, regardless of their circumstances.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8" data-aos="fade-right">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-indigo-100">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">Global Impact</h3>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      For over 15 years, we've been transforming lives across communities, providing comprehensive
                      support that addresses the root causes of poverty and inequality.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-purple-100">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">Holistic Approach</h3>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      Our integrated programs ensure sustainable change by addressing education, healthcare, shelter,
                      and community development simultaneously.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-8 rounded-3xl text-white text-center shadow-xl">
                  <div className="text-4xl font-black mb-2">25+</div>
                  <div className="text-lg font-semibold">Children in Care</div>
                </div>
                <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-8 rounded-3xl text-white text-center shadow-xl">
                  <div className="text-4xl font-black mb-2">100%</div>
                  <div className="text-lg font-semibold">School Enrollment</div>
                </div>
              </div>
            </div>

            <div className="relative" data-aos="fade-left">
              <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Children learning"
                  className="w-full h-[600px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>

                <div className="absolute bottom-8 left-8 right-8 text-white">
                  <div className="bg-white/20 backdrop-blur-md rounded-2xl p-6 border border-white/30">
                    <blockquote className="text-xl italic mb-4 leading-relaxed">
                      "Education is the most powerful weapon you can use to change the world. We believe in empowering
                      every child with this weapon."
                    </blockquote>
                    <div className="flex items-center space-x-4">
                      <div className="w-14 h-14 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <Heart className="w-7 h-7" />
                      </div>
                      <div>
                        <div className="font-bold text-lg">Hope Foundation</div>
                        <div className="text-indigo-200">Founding Principle</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Programs Section */}
      <section id="programs" className="py-24 bg-white relative overflow-hidden" data-aos="fade-up">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-indigo-50/30"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full px-6 py-3 mb-6">
              <Sparkles className="w-5 h-5 text-indigo-600 mr-2" />
              <span className="text-indigo-800 font-semibold">OUR PROGRAMS</span>
            </div>

            <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
              Comprehensive
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                {" "}
                Solutions
              </span>
            </h2>

            <p className="text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Multi-faceted programs designed to create lasting change and empower communities to thrive independently.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {programs.map((program, index) => (
              <div
                key={index}
                className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:scale-[1.02]"
                data-aos="fade-up"
                data-aos-delay={index * 150}
              >
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={program.image || "/placeholder.svg"}
                    alt={program.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute top-6 left-6">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${program.color} rounded-2xl flex items-center justify-center shadow-lg`}
                    >
                      {program.icon}
                    </div>
                  </div>
                  <div className="absolute top-6 right-6">
                    <div
                      className={`bg-gradient-to-r ${program.color} text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg`}
                    >
                      {program.beneficiaries}
                    </div>
                  </div>
                </div>

                <div className="p-8">
                  <h3 className="text-3xl font-black text-gray-900 mb-4 group-hover:text-indigo-600 transition-colors duration-300">
                    {program.title}
                  </h3>

                  <p className="text-gray-700 text-lg leading-relaxed mb-6">{program.description}</p>

                  <button
                    className={`group/btn inline-flex items-center space-x-2 bg-gradient-to-r ${program.color} text-white px-6 py-3 rounded-full font-bold hover:scale-105 transition-all duration-300 shadow-lg`}
                  >
                    <span>Learn More</span>
                    <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Impact Gallery */}
      <section
        id="impact"
        className="py-24 bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden"
        data-aos="fade-up"
      >
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-r from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-r from-rose-200/20 to-pink-200/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-gradient-to-r from-rose-100 to-pink-100 rounded-full px-6 py-3 mb-6">
              <Heart className="w-5 h-5 text-rose-600 mr-2" />
              <span className="text-rose-800 font-semibold">IMPACT STORIES</span>
            </div>

            <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
              Stories of
              <span className="bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 bg-clip-text text-transparent">
                Transformation
              </span>
            </h2>

            <p className="text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Witness the incredible journeys of hope, resilience, and triumph that define our mission
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {impactStories.map((story, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer hover:scale-105"
                data-aos="zoom-in"
                data-aos-delay={index * 100}
              >
                <div className="relative h-80">
                  <img
                    src={story.image || "/placeholder.svg"}
                    alt={story.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>

                  <div className="absolute top-4 left-4">
                    <div
                      className={`bg-gradient-to-r ${story.color} text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg`}
                    >
                      {story.category}
                    </div>
                  </div>

                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <h3 className="text-2xl font-black mb-3 group-hover:text-yellow-300 transition-colors duration-300">
                      {story.title}
                    </h3>
                    <p className="text-lg opacity-90 leading-relaxed">{story.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section
        className="py-24 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 relative overflow-hidden"
        data-aos="fade-up"
      >
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-black/20"></div>
          <div className="absolute top-20 left-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <Sparkles className="w-16 h-16 text-yellow-300 mx-auto mb-6 animate-pulse" />
          </div>

          <h2 className="text-5xl md:text-7xl font-black text-white mb-8 leading-tight">
            Ready to Make a
            <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              Difference?
            </span>
          </h2>

          <p className="text-2xl text-indigo-100 mb-12 max-w-4xl mx-auto leading-relaxed">
            Join thousands of changemakers who are creating a better world. Your support can transform lives and build
            stronger communities.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button className="group relative bg-white text-indigo-600 px-12 py-5 rounded-full font-black text-xl shadow-2xl hover:scale-105 transition-all duration-300 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center space-x-3">
                <Heart className="w-6 h-6 text-rose-500" />
                <span>Donate Now</span>
                <Sparkles className="w-6 h-6 text-yellow-500" />
              </div>
            </button>

            <button className="group border-2 border-white text-white px-12 py-5 rounded-full font-black text-xl hover:bg-white hover:text-indigo-600 transition-all duration-300 shadow-2xl">
              <div className="flex items-center space-x-3">
                <Users className="w-6 h-6 group-hover:scale-110 transition-transform" />
                <span>Volunteer With Us</span>
              </div>
            </button>
          </div>
        </div>
      </section>

      {/* Enhanced Contact Section */}
      <section
        id="contact"
        className="py-24 bg-gradient-to-br from-gray-50 to-indigo-50 relative overflow-hidden"
        data-aos="fade-up"
      >
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-indigo-200/30 to-purple-200/30 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-rose-200/30 to-pink-200/30 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full px-6 py-3 mb-6">
              <Mail className="w-5 h-5 text-indigo-600 mr-2" />
              <span className="text-indigo-800 font-semibold">GET IN TOUCH</span>
            </div>

            <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
              Let's Create
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Change Together
              </span>
            </h2>

            <p className="text-2xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              Ready to join our mission? We'd love to connect with passionate individuals who want to make a difference.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Phone className="w-12 h-12" />,
                title: "Call Us",
                info: "+91 80 2345 6789",
                color: "from-indigo-500 to-purple-600",
                bgColor: "from-indigo-50 to-purple-50",
              },
              {
                icon: <Mail className="w-12 h-12" />,
                title: "Email Us",
                info: "<EMAIL>",
                color: "from-purple-500 to-pink-600",
                bgColor: "from-purple-50 to-pink-50",
              },
              {
                icon: <MapPin className="w-12 h-12" />,
                title: "Visit Us",
                info: "Bangalore, Karnataka",
                color: "from-emerald-500 to-teal-600",
                bgColor: "from-emerald-50 to-teal-50",
              },
            ].map((contact, index) => (
              <div
                key={index}
                className={`group relative bg-gradient-to-br ${contact.bgColor} p-10 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 border border-white hover:scale-105 overflow-hidden`}
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${contact.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                ></div>

                <div className="relative text-center">
                  <div
                    className={`inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-r ${contact.color} mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <div className="text-white">{contact.icon}</div>
                  </div>

                  <h3 className="text-2xl font-black text-gray-900 mb-4 group-hover:text-indigo-600 transition-colors duration-300">
                    {contact.title}
                  </h3>

                  <p className="text-lg font-semibold text-gray-700">{contact.info}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Footer */}
      <footer
        className="bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 text-white py-16 relative overflow-hidden"
        data-aos="fade-up"
      >
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400"></div>
          <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4 mb-8">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
              </div>
              <div>
                <span className="text-4xl font-black tracking-tight">Hope Foundation</span>
                <div className="text-indigo-300 text-sm font-semibold">Transforming Lives Since 2009</div>
              </div>
            </div>

            <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto leading-relaxed">
              Together, we're building a world where every child has the opportunity to thrive, learn, and reach their
              full potential.
            </p>

            <div className="flex justify-center space-x-6 mb-8">
              {[
                { icon: <Heart className="w-6 h-6" />, label: "Love" },
                { icon: <Users className="w-6 h-6" />, label: "Community" },
                { icon: <BookOpen className="w-6 h-6" />, label: "Education" },
                { icon: <Award className="w-6 h-6" />, label: "Excellence" },
              ].map((value, index) => (
                <div key={index} className="flex flex-col items-center space-y-2 group">
                  <div className="w-12 h-12 bg-white/10 rounded-2xl flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                    {value.icon}
                  </div>
                  <span className="text-sm font-semibold text-indigo-200">{value.label}</span>
                </div>
              ))}
            </div>

            <div className="border-t border-indigo-800 pt-8">
              <p className="text-indigo-200 text-lg">© 2024 Hope Foundation. All rights reserved. | Reg No: 12345678</p>
              <p className="text-indigo-300 text-sm mt-2">Made with ❤️ for a better tomorrow</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default ModernCharityWebsite
