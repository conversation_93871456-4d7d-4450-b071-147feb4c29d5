import React from 'react';
import footer from './../assets/footer_bg.jpg';
import logo from './../assets/Logo.jpeg';
import { Mail, MapPinHouse, PhoneCall } from 'lucide-react';
import { Separator } from './ui/separator';
import { Link } from 'react-router-dom';

const Footer = () => {
    return (
        <div className='relative'>
            <div>
                <img src={footer} alt="" className='absolute lg:h-[70vh] w-full object-cover h-[130vh]' />
                <div className='absolute inset-0 bg-black opacity-40'></div>
                <div className='absolute inset-0 text-white px-[5vw] flex flex-col lg:flex-row justify-between gap-5 my-5'>
                    <div className='w-full lg:w-[40%]'>
                        <span className='flex items-center poppins-black text-2xl gap-2'>
                            <img src={logo} alt="" className='w-20' />
                            Mother Theresa Educational & Welfare Society
                        </span>
                        <p className='text-[14px] text-gray-50'>
                            Mother Theresa Educational & Welfare Society is a Voluntary organization working for the upliftment of the rural Poor and needy people with a non Profit motive as a Non Governmental Organization. Mother Theresa Educational & Welfare Society (MTEWS) is also registered with Union Home Ministry Under the F.C.R.A and obtained a registered Number : ********* and also it is Tax exempted with 12(A) from the Income tax Department with Number : I(86)/CIT/VJA/07-08. It was registered under the societies Registration act XXI of 1860, Regd. no. 182 / 1990.
                        </p>
                    </div>

                    <div className='w-full lg:w-[15%]'>
                        <h1 className='poppins-black text-2xl'>Quick Links</h1>
                      <Link to="/" >  <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Home</p></Link>
                       <Link to='/our_history'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>About Us</p></Link>
                       <Link to='/service'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Our Service</p></Link>
                       <Link to='/testimonial'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Testimonial</p></Link>
                       <Link to='/contact_us'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Contact Us</p></Link>
                    </div>

                    <div className='w-full lg:w-[15%]'>
                        <h1 className='poppins-black text-2xl'>Donations</h1>
                        <Link to="/donate"><p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Donate to Children</p></Link>
                       <Link to='/donate'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Donate to Elderly people</p></Link>
                      <Link to='/donate'>  <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Donate to Schools</p></Link>
                       <Link to='/donate'> <p className='text-[14px] text-gray-50 hover:text-[#fd7e14] cursor-pointer'>Donate to OrphanAge</p></Link>
                    </div>

                    <div className='w-full lg:w-[30%]'>
                        <h1 className='poppins-black text-2xl'>Contact Us</h1>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'>
                                <MapPinHouse className='text-white size-10' />
                            </div>
                            <p className='text-gray-50 text-[14px]'>
                                Dr. Joshi Kumar Jetty, chief Functionary, v#8-171, Lucas Nagar, KATURU Road,
                                VUYYURU 521165, Krishna District, Andhra Pradesh, India.
                            </p>
                        </div>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'>
                                <Mail className='text-white size-10' />
                            </div>
                            <p className='text-gray-50 text-[14px]'><EMAIL></p>
                        </div>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'>
                                <PhoneCall className='text-white size-10' />
                            </div>
                            <p className='text-gray-50 text-[14px]'>9440041850/7731041850</p>
                        </div>
                    </div>
                </div>
            </div>
            <Separator />
            <div className='text-center my-3 text-white'>
                © 2007 - 2024 Mother Teresa Educational and Welfare Society.
            </div>
        </div>
    );
}

export default Footer;
