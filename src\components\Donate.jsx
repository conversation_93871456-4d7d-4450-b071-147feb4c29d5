import React, { useState } from 'react';
import donate_banner from './../assets/donate_banner.jpg';
import { Card } from './ui/card';
import { Button } from './ui/button';
import payment from './../assets/payment-icons.webp';
import { Info, Landmark } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import promote from './../assets/akc-promote-01.webp';
import tax from './../assets/akc-tax-01.webp';
import transparency from './../assets/akc-transparency-01.webp';
import volunteering from './../assets/akc-volunteering-01.webp';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Link } from 'react-router-dom';
import qrcode from './../assets/qrcode.png';
import banner from './../assets/footer-2--bg.png';




const Donate = () => {
    const [donationType, setDonationType] = useState('Indian');
    const [selectedAmount, setSelectedAmount] = useState('');
    const [inputValue, setInputValue] = useState('');

    const handleAmountClick = (amount) => {
        setSelectedAmount(amount);
        setInputValue(amount);
    };

    const handleDonationTypeChange = (type) => {
        setDonationType(type);
        setSelectedAmount('');
        setInputValue('');
    };

    return (
        <div className="relative">
            <div className='relative'>
                <div>
                    <img src={donate_banner} alt="" className='h-[70vh] w-full object-cover' />
                </div>
                <div className='absolute top-0 left-0 w-full h-full bg-black opacity-40'></div>
                <h1 className='absolute text-4xl text-white poppins-black top-[200px] w-96 px-[5vw]'>
                    Your Support Matters: Donate to Change Lives
                </h1>
            </div>

            <div className='px-[5vw] mt-10 md:mt-0'>
                <Card className='p-3 w-full md:w-[380px] mx-auto md:float-end md:sticky mt-10'>
                    <div>
                        <div className='flex gap-5'>
                            <span className='flex items-center gap-2'>
                                <input
                                    type="radio"
                                    checked={donationType === 'Indian'}
                                    onChange={() => handleDonationTypeChange('Indian')}
                                /> Indian
                            </span>
                            <span className='flex items-center gap-2'>
                                <input
                                    type="radio"
                                    checked={donationType === 'Foreign'}
                                    onChange={() => handleDonationTypeChange('Foreign')}
                                /> Foreign
                            </span>
                        </div>
                    </div>

                    {donationType === 'Indian' && (
                        <>
                            <div className='flex my-4'>
                                <div className='bg-[#fd7e14] h-[30px] flex justify-center items-center p-3 text-white'>
                                    Give Once
                                </div>
                                <div className='h-[30px] flex justify-center items-center p-3 text-black border'>
                                    Give Monthly
                                </div>
                            </div>
                            <h1 className='font-bold'>Please select your donation amount</h1>
                            <div className='flex flex-wrap gap-5 mt-3'>
                                {['50000', '25000', '10000', '5000', '2500'].map((amount) => (
                                    <div
                                        key={amount}
                                        className={`w-[90px] border p-2 rounded-md flex justify-center items-center cursor-pointer ${selectedAmount === amount ? 'bg-[#fd7e14] text-white' : ''}`}
                                        onClick={() => handleAmountClick(amount)}
                                    >
                                        {amount}
                                    </div>
                                ))}
                            </div>
                            <input
                                type="text"
                                className='w-full py-2 border border-gray-300 rounded-md my-3'
                                value={inputValue}
                                onChange={(e) => setInputValue(e.target.value)}
                                placeholder='Other amount'
                            />
                            <p>You Pledge to serve <span className='font-semibold'>63</span> nutritious meals.</p>
                            <Button className='bg-[#fd7e14] w-full'>
                                <Dialog >
                                    <DialogTrigger>Proceed to Donate</DialogTrigger>
                                    <DialogContent className='max-w-[700px] w-full'>
                                        <DialogHeader>
                                            <DialogTitle className='flex gap-3 items-center'><Landmark />Confirm Your Contribution</DialogTitle>
                                            <DialogDescription>
                                                <div className='flex gap-10 flex-col lg:flex-row'>
                                                    <img src={qrcode} alt="" className='lg:w-[50%] mx-auto w-[60%]' />
                                                    <div className='w-[100%]'>
                                                        <h1>Please attach a screenshot of your bank transfer to help us verify your generous donation</h1>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Name:<span className='text-red-600'>*</span></label>
                                                            <input type="text" placeholder='Enter your Name' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Amount:<span className='text-red-600'>*</span></label>
                                                            <input type="number" placeholder='Enter your donated Amount' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Email:<span className='text-red-600'>*</span></label>
                                                            <input type="email" placeholder='Enter your Email' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>

                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Mobile:<span className='text-red-600'>*</span></label>
                                                            <input type="number" placeholder='Enter your Mobile Number' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Upload a Screenshot of your Donation:<span className='text-red-600'>*</span></label>
                                                            <input type="file" className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='flex justify-center items-center'>
                                                            <Button className='bg-[#fd7e14] px-10 hover:bg-[#0e1d81]'>Submit</Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog>
                            </Button>
                            <span className='flex items-center gap-2 text-sm my-2'>
                                <Info /> Donations are tax exempted under 80G
                            </span>

                            <Separator />
                            <h1>For Bank Transaction
                                <Dialog >
                                    <DialogTrigger className='text-[#fd7e14] ml-3 mt-2'>Click here</DialogTrigger>
                                    <DialogContent >
                                        <DialogHeader>
                                            <DialogTitle className='flex gap-3 items-center'><Landmark />Confirm Your Contribution</DialogTitle>
                                            <DialogDescription>
                                                <h1>Please attach a screenshot of your bank transfer to help us verify your generous donation</h1>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Name:<span className='text-red-600'>*</span></label>
                                                    <input type="text" placeholder='Enter your Name' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Amount:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your donated Amount' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Email:<span className='text-red-600'>*</span></label>
                                                    <input type="email" placeholder='Enter your Email' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>

                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Mobile:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your Mobile Number' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Upload a Screenshot of your Donation:<span className='text-red-600'>*</span></label>
                                                    <input type="file" className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='flex justify-center items-center'>
                                                    <Button className='bg-[#fd7e14] px-10 hover:bg-[#0e1d81]'>Submit</Button>
                                                </div>
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog>
                            </h1>
                        </>
                    )}

                    {donationType === 'Foreign' && (
                        <>
                            <h1 className='font-bold'>Please select your donation amount</h1>
                            <div className='flex flex-wrap gap-5 mt-3'>
                                {['600', '300', '200', '100', '50'].map((amount) => (
                                    <div
                                        key={amount}
                                        className={`w-[90px] border p-2 rounded-md flex justify-center items-center cursor-pointer ${selectedAmount === amount ? 'bg-[#fd7e14] text-white' : ''}`}
                                        onClick={() => handleAmountClick(amount)}
                                    >
                                        {amount} USD
                                    </div>
                                ))}
                            </div>
                            <input
                                type="text"
                                className='w-full py-2 border border-gray-300 rounded-md my-3'
                                value={inputValue}
                                onChange={(e) => setInputValue(e.target.value)}
                                placeholder='Other amount'
                            />
                            <p>You Pledge to serve <span className='font-semibold'>63</span> nutritious meals.</p>
                            <Button className='bg-[#fd7e14] w-full'>
                                <Dialog >
                                    <DialogTrigger>Proceed to Donate</DialogTrigger>
                                    <DialogContent className='max-w-[700px] w-full'>
                                        <DialogHeader>
                                            <DialogTitle className='flex gap-3 items-center'><Landmark />Confirm Your Contribution</DialogTitle>
                                            <DialogDescription>
                                                <div className='flex gap-10 flex-col lg:flex-row'>
                                                    <img src={qrcode} alt="" className='lg:w-[50%] mx-auto w-[60%]' />
                                                    <div className='w-[100%]'>
                                                        <h1>Please attach a screenshot of your bank transfer to help us verify your generous donation</h1>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Name:<span className='text-red-600'>*</span></label>
                                                            <input type="text" placeholder='Enter your Name' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Amount:<span className='text-red-600'>*</span></label>
                                                            <input type="number" placeholder='Enter your donated Amount' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Email:<span className='text-red-600'>*</span></label>
                                                            <input type="email" placeholder='Enter your Email' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>

                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Mobile:<span className='text-red-600'>*</span></label>
                                                            <input type="number" placeholder='Enter your Mobile Number' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='my-3'>
                                                            <label htmlFor="" className='text-black'>Upload a Screenshot of your Donation:<span className='text-red-600'>*</span></label>
                                                            <input type="file" className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                        </div>
                                                        <div className='flex justify-center items-center'>
                                                            <Button className='bg-[#fd7e14] px-10 hover:bg-[#0e1d81]'>Submit</Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog>
                            </Button>
                            <span className='flex items-center gap-2 text-sm my-2'>
                                <Info /> Donations are tax exempted under 80G
                            </span>

                            <Separator />
                            <h1>For Bank Transaction
                                {/* <Dialog style={{ width: '1000px' }}>
                                    <DialogTrigger className='text-[#fd7e14] ml-3 mt-2'>Click here</DialogTrigger>
                                    <DialogContent >
                                        <DialogHeader>
                                            <DialogTitle className='flex gap-3 items-center'><Landmark />Confirm Your Contribution</DialogTitle>
                                            <DialogDescription>
                                                <h1>Please attach a screenshot of your bank transfer to help us verify your generous donation</h1>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Name:<span className='text-red-600'>*</span></label>
                                                    <input type="text" placeholder='Enter your Name' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Amount:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your donated Amount' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Email:<span className='text-red-600'>*</span></label>
                                                    <input type="email" placeholder='Enter your Email' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>

                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Mobile:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your Mobile Number' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Upload a Screenshot of your Donation:<span className='text-red-600'>*</span></label>
                                                    <input type="file" className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='flex justify-center items-center'>
                                                    <Button className='bg-[#fd7e14] px-10 hover:bg-[#0e1d81]'>Submit</Button>
                                                </div>
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog> */}
                                <Dialog>
                                    <DialogTrigger className='text-[#fd7e14] ml-3 mt-2'>Click here</DialogTrigger>
                                    <DialogContent >
                                        <DialogHeader>
                                            <DialogTitle className='flex gap-3 items-center'><Landmark />Confirm Your Contribution</DialogTitle>
                                            <DialogDescription>
                                                <h1>Please attach a screenshot of your bank transfer to help us verify your generous donation</h1>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Name:<span className='text-red-600'>*</span></label>
                                                    <input type="text" placeholder='Enter your Name' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Amount:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your donated Amount' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Email:<span className='text-red-600'>*</span></label>
                                                    <input type="email" placeholder='Enter your Email' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>

                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Mobile:<span className='text-red-600'>*</span></label>
                                                    <input type="number" placeholder='Enter your Mobile Number' className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='my-3'>
                                                    <label htmlFor="" className='text-black'>Upload a Screenshot of your Donation:<span className='text-red-600'>*</span></label>
                                                    <input type="file" className='py-2 w-full border border-gray-500 rounded-md p-3' />
                                                </div>
                                                <div className='flex justify-center items-center'>
                                                    <Button className='bg-[#fd7e14] px-10 hover:bg-[#0e1d81]'>Submit</Button>
                                                </div>
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog>
                            </h1>
                        </>
                    )}
                </Card>
            </div>

            <div className='w-full md:w-[60%] px-[5vw]  my-28 md:mt-10'>
                <h1 className='text-2xl text-[#0e1d81]'>
                    Why Donate With <span className='font-bold barlow-black'>MOTHER THERESA EDUCATIONAL AND WELFARE SOCIETY</span>?
                </h1>
                <div className='flex justify-center items-center mb-5'>
                    <div className='w-[25%] h-1 bg-[#fd7e14] rounded-md'></div>
                </div>
                <div className='flex flex-wrap gap-5'>
                    <div className='border border-[#0e1d81] w-full md:w-[45%] h-[130px] rounded-md p-2'>
                        <div className='flex justify-center items-center'>
                            <img src={promote} alt="" className='w-24' />
                        </div>
                        <h3 className='text-center text-sm'>Transparent & Accountability</h3>
                    </div>
                    <div className='border border-[#0e1d81] w-full md:w-[45%] h-[130px] rounded-md p-2'>
                        <div className='flex justify-center items-center'>
                            <img src={tax} alt="" className='w-24' />
                        </div>
                        <h3 className='text-center text-sm'>Tax benefit on Donation</h3>
                    </div>
                    <div className='border border-[#0e1d81] w-full md:w-[45%] h-[130px] rounded-md p-2'>
                        <div className='flex justify-center items-center'>
                            <img src={transparency} alt="" className='w-24' />
                        </div>
                        <h3 className='text-center text-sm'>Promote good health & promotion</h3>
                    </div>
                    <div className='border border-[#0e1d81] w-full md:w-[45%] h-[130px] rounded-md p-2'>
                        <div className='flex justify-center items-center'>
                            <img src={volunteering} alt="" className='w-24' />
                        </div>
                        <h3 className='text-center text-sm'>Volunteering opportunity</h3>
                    </div>
                </div>
            </div>

            <div className=' px-[5vw] flex lg:justify-between gap-10 lg:items-center text-white lg:flex-row flex-col p-3'
              style={{
                backgroundImage:`url(${banner})`,
                backgroundPosition:'center',
                backgroundSize:"cover",
                width:"100%",
                height:"100%"
             }}
            >
                <img src={qrcode} alt="" className='lg:w-60 w-full' />
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>PhonePe:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>GooglePay:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>PayPal:</h1>
                    <p><EMAIL></p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
            </div>
        </div>
    );
}

export default Donate;
