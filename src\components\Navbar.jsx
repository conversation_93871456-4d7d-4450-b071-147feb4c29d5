import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import <PERSON><PERSON> from 'lottie-react';
import donate from './../assets/Animation - 1732176081754.json';
import logo from './../assets/Logo-removebg-preview.png';
import { Landmark, Menu, X } from 'lucide-react'; // Importing Lucide icons
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"





const Navbar = () => {
    const [isOpen, setIsOpen] = useState(false);

    const toggleMenu = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className='px-[5vw] flex justify-between items-center z-10 bg-white sticky top-0 border-b border-[#0e1d81] shadow-2xl'>
            <Link to='/'>  <img src={logo} alt="" className='w-24' /></Link>
            <div className='hidden md:flex items-center gap-5'>
                <Link to='/'> <p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Home</p></Link>
                <Link to='/our_history'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>About Us</p></Link>
                <Link to='/service'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Objective</p></Link>
                <Link to='/gallery'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Gallery</p></Link>
                <Link to='/testimonials'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Our Achievements</p></Link>
               
                <Link to='/donate'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Donate Us</p></Link>
                 <Link to='/contactus'><p className='lg:text-xl text-lg font-medium hover:text-[#fd7e14]'>Contact Us</p></Link>
                <Link to='/donate'>
                    <button className='w-32'>
                        <Lottie animationData={donate} />
                    </button>
                </Link>
            </div>
            <div className='md:hidden'>
                <button onClick={toggleMenu}>
                    {isOpen ? <X size={24} /> : <Menu size={24} />}
                </button>
            </div>
            {isOpen && (
                <div className='absolute top-16 left-0 w-full bg-white flex flex-col items-center gap-5 py-5 border-t border-[#0e1d81] shadow-lg md:hidden'>
                    <Link to='/' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Home</p></Link>
                    <Link to='/our_history' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Our History</p></Link>
                    <Link to='/service' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Our Service</p></Link>
                    <Link to='/gallery' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Gallery</p></Link>
                    <Link to='/testimonials' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Testimonial</p></Link>
                    <Link to='/contact' onClick={toggleMenu}><p className='text-lg font-medium hover:text-[#fd7e14]'>Contact Us</p></Link>
                    <Link to='/donate' onClick={toggleMenu}>
                        <button className='w-32'>
                            <Lottie animationData={donate} />
                        </button>
                    </Link>
                </div>
            )}
        </div>
    );
};

export default Navbar;
