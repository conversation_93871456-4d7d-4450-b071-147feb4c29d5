import React from 'react'
import contact from './../assets/portrait-male-friends-sharing-affectionate-moment-friendship_23-2151310371.jpg';
import { Card } from './ui/card';
import { Mail, MapPinHouse, PhoneCall } from 'lucide-react';
import { <PERSON><PERSON> } from './ui/button';
import qrcode from './../assets/qrcode.png';
import banner from './../assets/footer-2--bg.png';




const Contact = () => {
    return (
        <div>
            <div className='relative'>
                <div><img src={contact} alt="" className='h-[40vh] w-full object-cover' /></div>
                <div className='absolute top-0 left-0 w-full h-full bg-black opacity-40'></div>
            </div>

            <div className='my-3'>
                <h1 className='text-center text-2xl  text-[#0e1d81] font-bold barlow-black'>Contact Us</h1>
                <div className='flex justify-center items-center mb-5'> <div className='w-[5%] h-1 bg-[#fd7e14] rounded-md'></div></div>
            </div>

            <div className='px-[5vw]'>
                <Card className='flex lg:justify-start  p-3 gap-10 lg:flex-row flex-col'>
                    <div className='lg:w-[40%] w-full'>
                        <h3 className='text-[#fd7e14] font-medium'>Get in Touch With Us</h3>
                        <h1 className='my-3'>Contact Us for any questions !</h1>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'> <MapPinHouse className='text-white size-10' /></div>
                            <p className='text-gray-600'>Dr. Joshi Kumar Jetty, chief Functionary, v#8-171, Lucas Nagar,
                                KATURU Road, VUYYURU 521165, Krishna District, Andhra Pradesh, India.</p>
                        </div>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'> <Mail className='text-white size-10' /></div>
                            <p className='text-gray-600'><EMAIL></p>
                        </div>
                        <div className='flex gap-3 justify-start my-3'>
                            <div className='w-10 h-10 bg-[#fd7e14] p-3 rounded-md flex justify-center items-center'> <PhoneCall className='text-white size-10' /></div>
                            <p className='text-gray-600'>**********/**********</p>
                        </div>
                    </div>

                    <div className='lg:w-[60%] flex flex-col w-full'>
                        <input type="text" placeholder='Enter your Name' className='border border-gray-500 py-2 rounded-md my-2 p-2' />
                        <input type="email" placeholder='Enter your Email address' className='border border-gray-500 py-2 rounded-md my-2 p-2' />
                        <input type="number" placeholder='Enter your Mobile Number' className='border border-gray-500 py-2 rounded-md my-2 p-2' />
                        <textarea name="" id="" rows={5} className='border border-gray-500 py-2 rounded-md my-2 p-2'></textarea>
                        <div className='flex justify-center items-center my-5'><Button className='bg-[#fd7e14] px-10'>SUBMIT</Button></div>
                    </div>
                </Card>
            </div>

            <Card className='my-20'>
                <div className="mapswrapper" style={{ background: '#fff', position: 'relative' }}>
                    <iframe
                        width="100%"
                        height="350"
                        loading="lazy"
                        allowFullScreen
                        src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&q=Mother%20Theresa%20Educational%20%26%20Welfare%20Society.%20Lucas%20Nagar%2C%20%20Katuru%20Road%20VUYYURU%20%E2%80%93%20521%20165%20Krishna%20(Dist)%20Andhra%20Pradesh%2C%20India&zoom=10&maptype=roadmap"
                        style={{ border: 0, position: 'relative', zIndex: 2 }}
                    ></iframe>
                    <a
                        href="https://www.zrivo.com/louisiana-paycheck-calculator"
                        style={{ color: 'rgba(0,0,0,0)', position: 'absolute', left: 0, top: 0, zIndex: 0 }}
                    >
                        Louisiana Paycheck Calculator
                    </a>
                </div>
            </Card>

            <div className=' px-[5vw] flex lg:justify-between gap-10 lg:items-center text-white lg:flex-row flex-col p-3'
                style={{
                    backgroundImage: `url(${banner})`,
                    backgroundPosition: 'center',
                    backgroundSize: "cover",
                    width: "100%",
                    height: "100%"
                }}
            >
                <img src={qrcode} alt="" className='lg:w-60 w-full' />
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>PhonePe:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>GooglePay:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>PayPal:</h1>
                    <p><EMAIL></p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
            </div>
        </div>
    )
}

export default Contact