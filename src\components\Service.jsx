import React from 'react'
import serviceBanner from './../assets/servicebanner.jpg';
import { Card } from './ui/card';
import basicNeeds from './../assets/basicneeds.jpg';
import medicalCare from './../assets/childrenyoga.jpg';
// import confidenceBuilding from './../assets/children-confidence-building.jpg';
// import educationFacilities from './../assets/children-education-facilities.jpg';
// import nationalIntegration from './../assets/national-integration.jpg';
// import publishing from './../assets/publishing-materials.jpg';
// import ruralDevelopment from './../assets/rural-development.jpg';
// import educationalInstitutions from './../assets/educational-institutions.jpg';
// import vocationalTraining from './../assets/vocational-training.jpg';
// import economicDevelopment from './../assets/economic-development.jpg';
// import humanResources from './../assets/human-resources.jpg';
// import childLabor from './../assets/child-labor-elimination.jpg';
// import medicalCamps from './../assets/medical-camps.jpg';
// import qrcode from './../assets/qrcode.png';
// import banner from './../assets/footer-bg.png';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';

const Service = () => {
    return (
        <div className="bg-gray-50">
            {/* Hero Banner */}
            <div className='relative'>
                <div>
                    <img
                        src={serviceBanner}
                        alt="Orphanage children"
                        className='h-[50vh] w-full object-cover object-top'
                    />
                </div>
                <div className='absolute top-0 left-0 w-full h-full bg-black opacity-40'></div>
                <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center'>
                    <h1 className='text-4xl font-bold text-white mb-4'>Serving Humanity Without Boundaries</h1>
                    <p className='text-xl text-white max-w-3xl mx-auto'>
                        Empowering orphaned children and communities through comprehensive welfare programs
                    </p>
                </div>
            </div>

            {/* Services Introduction */}
            <div className='py-12 bg-white'>
                <div className='max-w-6xl mx-auto px-4'>
                    <h1 className='text-3xl text-center text-[#0e1d81] font-bold mb-2'>Our Comprehensive Services</h1>
                    <div className='flex justify-center mb-8'>
                        <div className='w-20 h-1 bg-[#fd7e14] rounded-md'></div>
                    </div>
                    <p className='text-lg text-center text-gray-700 mb-8'>
                        We provide holistic support to orphanage children irrespective of caste, creed, sex, or religion,
                        executing programs with no profit motive to transform lives and communities.
                    </p>
                </div>
            </div>

            {/* Service Cards */}
            <div className='max-w-6xl mx-auto px-4 py-8'>

                {/* Basic Needs */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row gap-8 items-center'>
                        <img
                            src={basicNeeds}
                            alt="Basic needs for children"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>Basic Necessities for Orphanage Children</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                We provide essential life-sustaining resources including:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Nutritious meals and clean drinking water</li>
                                <li>Safe shelter and comfortable living spaces</li>
                                <li>Quality clothing for all seasons</li>
                                <li>Personal hygiene kits and sanitation facilities</li>
                                <li>Daily essential supplies and care packages</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Support Basic Needs
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* Medical Care */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row-reverse gap-8 items-center'>
                        <img
                            src={medicalCare}
                            alt="Medical care and yoga"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>Health & Wellness Programs</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                Comprehensive health initiatives including:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Regular medical check-ups and treatments</li>
                                <li>Vaccination drives and preventive healthcare</li>
                                <li>Daily yoga and meditation sessions</li>
                                <li>Structured physical exercise programs</li>
                                <li>Mental health counseling and support</li>
                                <li>Nutritional guidance and supplements</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Support Health Initiatives
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* Confidence Building */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row gap-8 items-center'>
                        <img
                            src={confidenceBuilding}
                            alt="Confidence building"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>Personality Development & Life Skills</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                Programs to build resilient, capable individuals:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Confidence-building workshops and activities</li>
                                <li>Emotional intelligence development</li>
                                <li>Problem-solving and critical thinking skills</li>
                                <li>Leadership training and team-building exercises</li>
                                <li>Mentorship programs with successful professionals</li>
                                <li>Life skills education for independent living</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Empower Children
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* Educational Facilities */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row-reverse gap-8 items-center'>
                        <img
                            src={educationFacilities}
                            alt="Educational facilities"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>Educational & Recreational Development</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                Holistic development opportunities:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Formal education through affiliated schools</li>
                                <li>After-school tutoring and learning support</li>
                                <li>Educational field trips and excursions</li>
                                <li>Sports facilities and athletic training</li>
                                <li>Arts, music, and cultural programs</li>
                                <li>Spiritual development and meditation spaces</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Fund Education
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* National Integration */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row gap-8 items-center'>
                        <img
                            src={nationalIntegration}
                            alt="National integration"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>National Integration & Unity</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                Fostering national pride and unity:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Cultural exchange programs across states</li>
                                <li>National festivals and heritage celebrations</li>
                                <li>Interfaith dialogues and understanding sessions</li>
                                <li>Patriotic education and constitutional values</li>
                                <li>Community service initiatives nationwide</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Promote Unity
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* Publishing */}
                <Card className='p-6 mb-12 shadow-xl'>
                    <div className='flex flex-col lg:flex-row-reverse gap-8 items-center'>
                        <img
                            src={publishing}
                            alt="Publishing materials"
                            className='lg:w-1/2 w-full rounded-lg shadow-md object-cover h-96'
                        />
                        <div>
                            <h1 className='text-2xl text-[#0e1d81] font-bold mb-3'>Educational Publishing</h1>
                            <div className='w-16 h-1 bg-[#fd7e14] rounded-md mb-6'></div>
                            <p className='text-gray-700 mb-4'>
                                Knowledge dissemination through:
                            </p>
                            <ul className='list-disc pl-6 mb-6 space-y-2'>
                                <li>Educational books and textbooks</li>
                                <li>Inspirational journals and newsletters</li>
                                <li>Informative brochures and pamphlets</li>
                                <li>Children's literature and learning materials</li>
                                <li>Digital publications and e-learning resources</li>
                            </ul>
                            <Link to='/donate'>
                                <Button className='bg-[#0e1d81] hover:bg-[#fd7e14] text-white rounded-full px-8 py-6 text-lg'>
                                    Support Publishing
                                </Button>
                            </Link>
                        </div>
                    </div>
                </Card>

                {/* Additional Services Grid */}
                <div className='grid grid-cols-1 md:grid-cols-2 gap-8 mb-16'>
                    {/* Rural Development */}
                    <Card className='p-6 h-full shadow-lg'>
                        <img
                            src={ruralDevelopment}
                            alt="Rural development"
                            className='w-full h-64 object-cover rounded-t-lg mb-4'
                        />
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Rural Development Initiatives</h2>
                        <p className='text-gray-700 mb-4'>
                            Sustainable development programs for rural communities including infrastructure improvement, agricultural support, and clean water projects.
                        </p>
                    </Card>

                    {/* Educational Institutions */}
                    <Card className='p-6 h-full shadow-lg'>
                        <img
                            src={educationalInstitutions}
                            alt="Educational institutions"
                            className='w-full h-64 object-cover rounded-t-lg mb-4'
                        />
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Educational Institutions</h2>
                        <p className='text-gray-700 mb-4'>
                            Establishing and maintaining schools, colleges, and specialized institutions from nursery to professional levels.
                        </p>
                    </Card>

                    {/* Vocational Training */}
                    <Card className='p-6 h-full shadow-lg'>
                        <img
                            src={vocationalTraining}
                            alt="Vocational training"
                            className='w-full h-64 object-cover rounded-t-lg mb-4'
                        />
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Vocational Training</h2>
                        <p className='text-gray-700 mb-4'>
                            Skill development programs in computer training, tailoring, handicrafts, and industrial skills for livelihood support.
                        </p>
                    </Card>

                    {/* Economic Development */}
                    <Card className='p-6 h-full shadow-lg'>
                        <img
                            src={economicDevelopment}
                            alt="Economic development"
                            className='w-full h-64 object-cover rounded-t-lg mb-4'
                        />
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Youth Economic Development</h2>
                        <p className='text-gray-700 mb-4'>
                            Sustainable economic programs for unemployed youth including entrepreneurship training and micro-finance support.
                        </p>
                    </Card>
                </div>

                {/* Additional Initiatives */}
                <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-16'>
                    <Card className='p-6 shadow-lg'>
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Human Resource Development</h2>
                        <p>
                            Educational, legal, and environmental awareness programs to strengthen community capabilities.
                        </p>
                    </Card>
                    <Card className='p-6 shadow-lg'>
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Elimination of Child Labor</h2>
                        <p>
                            Rescue, rehabilitation, and education programs for children removed from labor situations.
                        </p>
                    </Card>
                    <Card className='p-6 shadow-lg'>
                        <h2 className='text-xl font-bold text-[#0e1d81] mb-3'>Medical Camps</h2>
                        <p>
                            Regular health camps providing free medical care and health education to underserved communities.
                        </p>
                    </Card>
                </div>
            </div>

            {/* Donation Section */}
            <div className='px-4 py-12 text-white' style={{
                backgroundImage: `url(${banner})`,
                backgroundPosition: 'center',
                backgroundSize: "cover",
            }}>
                <div className='max-w-6xl mx-auto'>
                    <h1 className='text-3xl font-bold text-center mb-12'>Support Our Mission</h1>

                    <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
                        <div className='bg-white/10 p-6 rounded-xl backdrop-blur-sm'>
                            <h2 className='text-xl font-bold mb-4'>Bank Transfer</h2>
                            <div className='space-y-2'>
                                <p><span className='font-semibold'>Name:</span> Mother Theresa Educational & Welfare Society</p>
                                <p><span className='font-semibold'>Bank:</span> State Bank of India</p>
                                <p><span className='font-semibold'>Branch:</span> Ramamurthy Nagar, Bangalore-560016</p>
                                <p><span className='font-semibold'>A/C No:</span> *************</p>
                                <p><span className='font-semibold'>IFSC:</span> SBIN0001234</p>
                                <p><span className='font-semibold'>Type:</span> Current Account</p>
                            </div>
                        </div>

                        <div className='bg-white/10 p-6 rounded-xl backdrop-blur-sm'>
                            <h2 className='text-xl font-bold mb-4'>Digital Payments</h2>
                            <div className='space-y-3'>
                                <p><span className='font-semibold'>PhonePe:</span> **********</p>
                                <p><span className='font-semibold'>Google Pay:</span> **********</p>
                                <p><span className='font-semibold'>PayPal:</span> <EMAIL></p>
                                <div className='flex justify-center mt-4'>
                                    <img src={qrcode} alt="QR Code" className='w-40 h-40 bg-white p-2 rounded-lg' />
                                </div>
                            </div>
                        </div>

                        <div className='bg-white/10 p-6 rounded-xl backdrop-blur-sm'>
                            <h2 className='text-xl font-bold mb-4'>Other Support</h2>
                            <p className='mb-4'>Your contributions help us provide:</p>
                            <ul className='list-disc pl-5 space-y-2 mb-6'>
                                <li>Education for 500+ children annually</li>
                                <li>Medical care for 2000+ beneficiaries</li>
                                <li>Daily meals for 300+ residents</li>
                                <li>Vocational training for 150+ youth</li>
                            </ul>
                            <div className='text-center'>
                                <Link to='/donate'>
                                    <Button className='bg-[#fd7e14] hover:bg-[#0e1d81] text-white rounded-full px-8 py-6 text-lg font-bold'>
                                        Donate Now
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Service