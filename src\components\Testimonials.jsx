import React from 'react';
import testimonials from './../assets/testimonials_banner.png';
import { Card } from './ui/card';
import { FaQuoteLeft } from "react-icons/fa";
import { FaQuoteRight } from "react-icons/fa";
import qrcode from './../assets/qrcode.png';
import banner from './../assets/footer-2--bg.png';


const Testimonials = () => {
    return (
        <div>
            <div className='relative'>
                <div><img src={testimonials} alt="" className='h-[40vh] w-full object-cover' /></div>
                <div className='absolute top-0 left-0 w-full h-full bg-black opacity-40'></div>
            </div>

            <div className='my-3'>
                <h1 className='text-center text-2xl  text-[#0e1d81] font-bold barlow-black'>Testimonials</h1>
                <div className='flex justify-center items-center mb-5'>
                    <div className='w-[8%] h-1 bg-[#fd7e14] rounded-md'></div>
                </div>
            </div>

            <div className='px-[5vw] my-10'>
                <Card className='border border-[#fd7e14] p-3'>
                    <FaQuoteLeft className='text-[#fd7e14] text-2xl' />
                    <p className=''> I am Aarju Sheoran, and I am currently studying at TAPMI, Bengaluru. I completed my social internship at Raise India Foundation.

                        Working as an intern at Raise India Foundation, specifically on the Shikshalaya project, has been an extraordinary experience. Shikshalaya provides free education to children, and being a part of this initiative was an incredibly rewarding experience. Every day was filled with new experiences and learnings. Teaching the children alongside the dedicated faculty members was not only fulfilling but also deeply motivating

                        Interacting with the children and helping them with their studies was an eye-opener for me. It made me appreciate the teaching profession and the challenges teachers face daily. I never thought I would find myself in the teaching field, but this internship allowed me to experience firsthand the difficulties and rewards of educating and managing young minds.

                        Working for a social cause like this has been a blessing. Seeing the happiness and progress in the children's lives and knowing I contributed to it brought immense joy. The children taught me valuable lessons that I will carry with me throughout my life.
                        Raise India Foundation is doing a commendable job. They not only provide education to these children but also treat them and their families as part of the Shikshalaya family. This holistic approach makes a significant impact, and I am proud to have been a part of it. </p>
                    <FaQuoteRight className='text-[#fd7e14] text-2xl float-end' />
                </Card>

                <Card className='border border-[#fd7e14] p-3 mt-10'>
                    <FaQuoteLeft className='text-[#fd7e14] text-2xl' />
                    <p className=''> I am Aarju Sheoran, and I am currently studying at TAPMI, Bengaluru. I completed my social internship at Raise India Foundation.

                        Working as an intern at Raise India Foundation, specifically on the Shikshalaya project, has been an extraordinary experience. Shikshalaya provides free education to children, and being a part of this initiative was an incredibly rewarding experience. Every day was filled with new experiences and learnings. Teaching the children alongside the dedicated faculty members was not only fulfilling but also deeply motivating

                        Interacting with the children and helping them with their studies was an eye-opener for me. It made me appreciate the teaching profession and the challenges teachers face daily. I never thought I would find myself in the teaching field, but this internship allowed me to experience firsthand the difficulties and rewards of educating and managing young minds.

                        Working for a social cause like this has been a blessing. Seeing the happiness and progress in the children's lives and knowing I contributed to it brought immense joy. The children taught me valuable lessons that I will carry with me throughout my life.
                        Raise India Foundation is doing a commendable job. They not only provide education to these children but also treat them and their families as part of the Shikshalaya family. This holistic approach makes a significant impact, and I am proud to have been a part of it. </p>
                    <FaQuoteRight className='text-[#fd7e14] text-2xl float-end' />
                </Card>
            </div>

            <div className=' px-[5vw] flex lg:justify-between gap-10 lg:items-center text-white lg:flex-row flex-col p-3'
              style={{
                backgroundImage:`url(${banner})`,
                backgroundPosition:'center',
                backgroundSize:"cover",
                width:"100%",
                height:"100%"
             }}
            >
                <img src={qrcode} alt="" className='lg:w-60 w-full' />
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>PhonePe:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>GooglePay:</h1>
                    <p>**********</p>
                    <h1 className='text-2xl poppins-black'>PayPal:</h1>
                    <p><EMAIL></p>
                </div>
                <div>
                    <h1 className='text-2xl poppins-black'>State Bank Of India</h1>
                    <p>Mother Theresa Educational & Welfare Society</p>
                    <p>A/c Type: Current Account</p>
                    <p>Branch: Ramamurthy Nagar</p>
                    <p>A/c No: *************</p>
                    <p>IFSC Code: SBIN0001234</p>
                    <p>Bangalore-560016.</p>
                </div>
            </div>
        </div>
    );
}

export default Testimonials;
